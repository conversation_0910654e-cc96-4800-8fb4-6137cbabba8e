<script setup lang="ts">
// 导入Naive UI的全局配置
import {
  NConfigProvider,
  zhCN,
  dateZhCN,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider
} from 'naive-ui'

// 定义组件选项
defineOptions({
  name: 'App'
})
</script>

<template>
  <n-config-provider :locale="zhCN" :date-locale="dateZhCN"> 
    <NMessageProvider>
      <NDialogProvider>
        <NNotificationProvider>
          <div id="app">
            <router-view />
          </div>
        </NNotificationProvider>
      </NDialogProvider>
    </NMessageProvider>
  </n-config-provider>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
