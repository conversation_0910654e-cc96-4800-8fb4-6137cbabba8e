<script setup lang="ts">
import {
  NConfigProvider,
  zhCN,
  dateZhCN,
  darkTheme,
  lightTheme
} from 'naive-ui'
import { isDark } from './composables/useTheme'

// 定义组件选项
defineOptions({
  name: 'App'
})
</script>

<template>
  <NConfigProvider
    :theme="isDark ? darkTheme : lightTheme"
    :locale="zhCN"
    :date-locale="dateZhCN"
  >
    <div id="app" :class="{ 'dark-theme': isDark }">
      <router-view />
    </div>
  </NConfigProvider>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗黑主题样式 */
.dark-theme {
  background-color: #101014;
  color: #ffffff;
}

.dark-theme ::-webkit-scrollbar-track {
  background: #2c2c32;
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: #48484e;
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: #5c5c66;
}
</style>
