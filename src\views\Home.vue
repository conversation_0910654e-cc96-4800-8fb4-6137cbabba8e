<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <div class="header">
      <n-button 
        text 
        @click="goHome"
        class="home-icon"
      >
        <n-icon size="24">
          <HomeIcon />
        </n-icon>
      </n-button>
      <h1 class="title">AI智能助手</h1>
    </div>

    <!-- 功能卡片区域 -->
    <div class="cards-container">
      <n-grid :cols="2" :x-gap="20" :y-gap="20">
        <n-grid-item>
          <n-card 
            hoverable 
            class="feature-card"
            @click="goToChat"
          >
            <div class="card-content">
              <n-icon size="48" class="card-icon">
                <ChatIcon />
              </n-icon>
              <h3>智能聊天</h3>
              <p>与AI助手进行对话交流</p>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card 
            hoverable 
            class="feature-card"
            @click="goToCustomerService"
          >
            <div class="card-content">
              <n-icon size="48" class="card-icon">
                <CustomerServiceIcon />
              </n-icon>
              <h3>智能客服</h3>
              <p>24小时在线客服支持</p>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card 
            hoverable 
            class="feature-card"
            @click="goToKnowledge"
          >
            <div class="card-content">
              <n-icon size="48" class="card-icon">
                <KnowledgeIcon />
              </n-icon>
              <h3>知识库</h3>
              <p>海量知识库查询</p>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card 
            hoverable 
            class="feature-card"
            @click="goToSettings"
          >
            <div class="card-content">
              <n-icon size="48" class="card-icon">
                <SettingsIcon />
              </n-icon>
              <h3>设置</h3>
              <p>个性化设置选项</p>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  NButton, 
  NIcon, 
  NCard, 
  NGrid, 
  NGridItem 
} from 'naive-ui'
import {
  Home as HomeIcon,
  ChatbubbleEllipses as ChatIcon,
  Headset as CustomerServiceIcon,
  Library as KnowledgeIcon,
  Settings as SettingsIcon
} from '@vicons/ionicons5'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goToChat = () => {
  router.push('/chat')
}

const goToCustomerService = () => {
  // 暂时跳转到聊天页面
  router.push('/chat')
}

const goToKnowledge = () => {
  // 暂时跳转到聊天页面
  router.push('/chat')
}

const goToSettings = () => {
  // 暂时跳转到聊天页面
  router.push('/chat')
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  padding: 20px 0;
}

.home-icon {
  color: white;
  margin-right: 16px;
}

.title {
  color: white;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.cards-container {
  max-width: 800px;
  margin: 0 auto;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 200px;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.card-icon {
  color: #667eea;
  margin-bottom: 16px;
}

.card-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.card-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}
</style>
