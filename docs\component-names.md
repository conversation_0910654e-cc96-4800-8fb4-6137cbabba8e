# 组件命名规范

本项目中所有组件都使用 `defineOptions` 定义了明确的组件名称，便于调试和开发工具识别。

## 页面组件 (Views)

| 文件路径 | 组件名称 | 描述 |
|---------|---------|------|
| `src/views/Home.vue` | `HomePage` | 首页，包含功能卡片导航 |
| `src/views/Chat.vue` | `ChatPage` | 聊天页面，包含侧边栏和聊天区域 |

## 通用组件 (Components)

| 文件路径 | 组件名称 | 描述 |
|---------|---------|------|
| `src/App.vue` | `App` | 根应用组件 |
| `src/components/ChatSidebar.vue` | `ChatSidebar` | 聊天侧边栏，显示历史对话列表 |
| `src/components/ChatArea.vue` | `ChatArea` | 聊天区域，包含消息列表和输入框 |
| `src/components/MessageItem.vue` | `MessageItem` | 单个消息项组件 |
| `src/components/HelloWorld.vue` | `HelloWorld` | 示例组件（未使用） |

## 使用 defineOptions 的好处

1. **调试友好**: 在 Vue DevTools 中可以清楚地看到组件名称
2. **错误追踪**: 错误堆栈中显示明确的组件名称
3. **代码可读性**: 提高代码的可维护性
4. **开发体验**: 更好的开发工具支持

## 命名规范

- **页面组件**: 使用 `XxxPage` 格式，如 `HomePage`、`ChatPage`
- **功能组件**: 使用描述性名称，如 `ChatSidebar`、`MessageItem`
- **通用组件**: 使用简洁明了的名称，如 `App`

## 示例代码

```vue
<script setup lang="ts">
// 定义组件选项
defineOptions({
  name: 'ComponentName'
})

// 其他组件逻辑...
</script>
```

## 注意事项

1. 组件名称应该使用 PascalCase 格式
2. 名称应该具有描述性，能够清楚表达组件的用途
3. 避免使用过于通用的名称，如 `Component`、`Item` 等
4. 页面组件建议添加 `Page` 后缀以区分功能组件
