# 主题切换功能说明

## 功能概述

项目现在支持暗黑/浅色主题切换功能，用户可以在首页右上角点击主题切换按钮来切换主题。

## 实现方案

### 1. 主题管理 (`src/composables/useTheme.ts`)

使用Vue3的Composition API创建了一个全局的主题管理工具：

```typescript
export const useTheme = () => {
  return {
    isDark: readonly(isDark),
    toggleTheme
  }
}
```

**特性**：
- 自动检测系统主题偏好
- 主题状态持久化到localStorage
- 响应式主题状态管理
- 监听系统主题变化

### 2. 全局主题配置 (`src/App.vue`)

使用Naive UI的`NConfigProvider`提供全局主题配置：

```vue
<NConfigProvider 
  :theme="isDark ? darkTheme : lightTheme"
  :locale="zhCN" 
  :date-locale="dateZhCN"
>
```

**包含**：
- 暗黑/浅色主题切换
- 中文本地化配置
- 全局样式类名绑定

### 3. 主题切换按钮 (`src/views/Home.vue`)

在首页右上角添加了主题切换按钮：

```vue
<n-button 
  text 
  @click="toggleTheme"
  class="theme-toggle"
  :title="isDark ? '切换到浅色主题' : '切换到深色主题'"
>
  <n-icon size="20">
    <SunnyIcon v-if="isDark" />
    <MoonIcon v-else />
  </n-icon>
</n-button>
```

**特性**：
- 动态图标（太阳/月亮）
- 悬停提示
- 平滑过渡动画

## 主题样式

### 浅色主题（默认）
- 背景：渐变色 `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- 文字：白色
- 滚动条：浅色系

### 暗黑主题
- 背景：深色 `#101014`
- 文字：白色
- 滚动条：深色系

```css
.dark-theme {
  background-color: #101014;
  color: #ffffff;
}
```

## 使用方法

### 在组件中使用主题

```vue
<script setup>
import { useTheme } from '@/composables/useTheme'

const { isDark, toggleTheme } = useTheme()

// 根据主题状态调整样式
const buttonClass = computed(() => ({
  'dark-button': isDark.value,
  'light-button': !isDark.value
}))
</script>
```

### 添加主题相关样式

```css
/* 浅色主题样式 */
.my-component {
  background: white;
  color: black;
}

/* 暗黑主题样式 */
.dark-theme .my-component {
  background: #2c2c32;
  color: white;
}
```

## 主题持久化

主题选择会自动保存到`localStorage`：

```typescript
// 保存主题
localStorage.setItem('theme', 'dark' | 'light')

// 读取主题
const savedTheme = localStorage.getItem('theme')
```

## 系统主题检测

自动检测用户的系统主题偏好：

```typescript
// 检测系统主题
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

// 监听系统主题变化
mediaQuery.addEventListener('change', (e) => {
  if (!localStorage.getItem('theme')) {
    isDark.value = e.matches
  }
})
```

## 扩展建议

### 1. 添加更多主题
可以扩展支持多种主题色彩：

```typescript
const themes = {
  light: lightTheme,
  dark: darkTheme,
  blue: blueTheme,
  green: greenTheme
}
```

### 2. 主题动画
添加主题切换的过渡动画：

```css
* {
  transition: background-color 0.3s ease, color 0.3s ease;
}
```

### 3. 组件级主题
为特定组件提供独立的主题配置：

```vue
<NConfigProvider :theme="customTheme">
  <MyComponent />
</NConfigProvider>
```

## 注意事项

1. **性能考虑**：主题切换是全局操作，会触发所有组件重新渲染
2. **样式优先级**：确保暗黑主题样式的CSS选择器优先级足够高
3. **图标适配**：确保图标在不同主题下都有良好的对比度
4. **测试覆盖**：在两种主题下都要测试UI组件的显示效果

## 文件结构

```
src/
├── composables/
│   └── useTheme.ts          # 主题管理工具
├── App.vue                  # 全局主题配置
├── views/
│   └── Home.vue            # 主题切换按钮
└── docs/
    └── theme-switching.md   # 本文档
```
