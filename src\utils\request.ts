import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showError?: boolean // 是否显示错误提示
  showLoading?: boolean // 是否显示加载状态
}

class HttpRequest {
  private instance: AxiosInstance

  constructor() {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })

    // 设置请求拦截器
    this.setupRequestInterceptors()

    // 设置响应拦截器
    this.setupResponseInterceptors()
  }

  // 请求拦截器
  private setupRequestInterceptors() {
    this.instance.interceptors.request.use(
      (config: any) => {
        // 添加认证token
        const token = localStorage.getItem('token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求时间戳，防止缓存
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now()
          }
        }

        console.log('🚀 请求发送:', config)
        return config
      },
      (error: AxiosError) => {
        console.error('❌ 请求错误:', error)
        return Promise.reject(error)
      }
    )
  }

  // 响应拦截器
  private setupResponseInterceptors() {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log('✅ 响应接收:', response)

        const { data } = response

        // 检查业务状态码
        if (data.code === 200 || data.success) {
          return data
        } else {
          // 业务错误处理
          this.handleBusinessError(data)
          return Promise.reject(new Error(data.message || '请求失败'))
        }
      },
      (error: AxiosError) => {
        console.error('❌ 响应错误:', error)
        this.handleHttpError(error)
        return Promise.reject(error)
      }
    )
  }

  // 处理业务错误
  private handleBusinessError(data: ApiResponse) {
    const errorMessage = this.getErrorMessage(data.code, data.message)
    console.error('业务错误:', errorMessage)

    // 对于认证错误，清除token并跳转
    if (data.code === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
  }

  // 获取错误消息
  private getErrorMessage(code: number, message?: string): string {
    switch (code) {
      case 401:
        return '登录已过期，请重新登录'
      case 403:
        return '没有权限访问该资源'
      case 404:
        return '请求的资源不存在'
      case 500:
        return '服务器内部错误'
      default:
        return message || '请求失败'
    }
  }

  // 处理HTTP错误
  private handleHttpError(error: AxiosError) {
    let errorMessage = '请求失败'

    if (error.response) {
      const { status } = error.response
      errorMessage = this.getHttpErrorMessage(status)

      // 对于认证错误，清除token并跳转
      if (status === 401) {
        localStorage.removeItem('token')
        window.location.href = '/login'
      }
    } else if (error.request) {
      errorMessage = '网络连接异常，请检查网络'
    } else {
      errorMessage = '请求配置错误'
    }

    console.error('HTTP错误:', errorMessage)
  }

  // 获取HTTP错误消息
  private getHttpErrorMessage(status: number): string {
    switch (status) {
      case 400:
        return '请求参数错误'
      case 401:
        return '未授权，请登录'
      case 403:
        return '拒绝访问'
      case 404:
        return '请求地址出错'
      case 408:
        return '请求超时'
      case 500:
        return '服务器内部错误'
      case 501:
        return '服务未实现'
      case 502:
        return '网关错误'
      case 503:
        return '服务不可用'
      case 504:
        return '网关超时'
      case 505:
        return 'HTTP版本不受支持'
      default:
        return `连接错误${status}`
    }
  }

  // GET请求
  public get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.get(url, config)
  }

  // POST请求
  public post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config)
  }

  // PUT请求
  public put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config)
  }

  // DELETE请求
  public delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.delete(url, config)
  }

  // PATCH请求
  public patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.patch(url, data, config)
  }

  // 上传文件
  public upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    return this.instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 下载文件
  public download(url: string, filename?: string, config?: RequestConfig): Promise<void> {
    return this.instance.get(url, {
      ...config,
      responseType: 'blob'
    }).then((response: any) => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 创建实例并导出
const request = new HttpRequest()
export default request
