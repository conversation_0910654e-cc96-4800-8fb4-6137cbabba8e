<template>
  <div class="chat-container">
    <!-- 顶部导航 -->
    <div class="chat-header">
      <n-button 
        text 
        @click="goHome"
        class="back-button"
      >
        <n-icon size="20">
          <ArrowBackIcon />
        </n-icon>
        返回首页
      </n-button>
      <h2 class="chat-title">AI智能聊天</h2>
    </div>

    <!-- 聊天主体 -->
    <div class="chat-body">
      <!-- 左侧历史聊天列表 -->
      <div class="sidebar">
        <ChatSidebar 
          :chat-list="chatList"
          :active-chat-id="activeChatId"
          @select-chat="selectChat"
          @pin-chat="pinChat"
          @rename-chat="renameChat"
          @delete-chat="deleteChat"
        />
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-area">
        <ChatArea 
          :messages="currentMessages"
          @send-message="sendMessage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { NButton, NIcon } from 'naive-ui'
import { ArrowBack as ArrowBackIcon } from '@vicons/ionicons5'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatArea from '../components/ChatArea.vue'

interface ChatItem {
  id: string
  title: string
  lastMessage: string
  timestamp: string
  isPinned: boolean
}

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  thinking?: string // deepseek-r1思考过程
}

const router = useRouter()

// 聊天列表数据
const chatList = ref<ChatItem[]>([
  {
    id: '1',
    title: '新的对话',
    lastMessage: '你好，我是AI助手',
    timestamp: '2024-01-20 10:30',
    isPinned: false
  },
  {
    id: '2',
    title: 'Vue3开发问题',
    lastMessage: '关于Vue3的组合式API...',
    timestamp: '2024-01-19 15:20',
    isPinned: true
  }
])

// 当前激活的聊天ID
const activeChatId = ref('1')

// 消息数据
const messagesMap = ref<Record<string, Message[]>>({
  '1': [
    {
      id: '1',
      type: 'assistant',
      content: '你好！我是AI助手，有什么可以帮助你的吗？',
      timestamp: '2024-01-20 10:30'
    }
  ],
  '2': [
    {
      id: '2',
      type: 'user',
      content: 'Vue3的组合式API有什么优势？',
      timestamp: '2024-01-19 15:20'
    },
    {
      id: '3',
      type: 'assistant',
      content: 'Vue3的组合式API有以下几个主要优势：\n\n1. 更好的逻辑复用\n2. 更好的TypeScript支持\n3. 更灵活的代码组织\n4. 更好的性能',
      timestamp: '2024-01-19 15:21',
      thinking: '用户询问Vue3组合式API的优势，我需要从多个角度来回答这个问题，包括代码复用、类型支持、代码组织和性能等方面。'
    }
  ]
})

// 当前聊天的消息
const currentMessages = computed(() => {
  return messagesMap.value[activeChatId.value] || []
})

const goHome = () => {
  router.push('/')
}

const selectChat = (chatId: string) => {
  activeChatId.value = chatId
}

const pinChat = (chatId: string) => {
  const chat = chatList.value.find(c => c.id === chatId)
  if (chat) {
    chat.isPinned = !chat.isPinned
  }
}

const renameChat = (chatId: string, newTitle: string) => {
  const chat = chatList.value.find(c => c.id === chatId)
  if (chat) {
    chat.title = newTitle
  }
}

const deleteChat = (chatId: string) => {
  const index = chatList.value.findIndex(c => c.id === chatId)
  if (index > -1) {
    chatList.value.splice(index, 1)
    delete messagesMap.value[chatId]
    
    // 如果删除的是当前聊天，切换到第一个聊天
    if (activeChatId.value === chatId && chatList.value.length > 0) {
      activeChatId.value = chatList.value[0].id
    }
  }
}

const sendMessage = (content: string) => {
  const newMessage: Message = {
    id: Date.now().toString(),
    type: 'user',
    content,
    timestamp: new Date().toLocaleString()
  }
  
  if (!messagesMap.value[activeChatId.value]) {
    messagesMap.value[activeChatId.value] = []
  }
  
  messagesMap.value[activeChatId.value].push(newMessage)
  
  // 模拟AI回复
  setTimeout(() => {
    const aiMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: `这是对"${content}"的回复。我正在处理您的问题...`,
      timestamp: new Date().toLocaleString(),
      thinking: '用户发送了一条消息，我需要仔细分析内容并给出合适的回复。'
    }
    messagesMap.value[activeChatId.value].push(aiMessage)
    
    // 更新聊天列表中的最后消息
    const chat = chatList.value.find(c => c.id === activeChatId.value)
    if (chat) {
      chat.lastMessage = aiMessage.content.substring(0, 20) + '...'
      chat.timestamp = aiMessage.timestamp
    }
  }, 1000)
}
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  margin-right: 16px;
  color: #666;
}

.chat-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chat-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
}

.chat-area {
  flex: 1;
  background: white;
}
</style>
