# API 接口文档

## 概述

本项目使用 axios 封装了完整的 HTTP 请求工具类，并提供了聊天机器人相关的 API 接口。

## 文件结构

```
src/
├── utils/
│   └── request.ts          # axios 工具类
├── api/
│   ├── chatbot.ts         # 聊天机器人 API
│   └── README.md          # 本文档
└── .env.development       # 开发环境配置
└── .env.production        # 生产环境配置
```

## 使用方法

### 1. 基础 HTTP 请求

```typescript
import request from '@/utils/request'

// GET 请求
const response = await request.get('/api/users')

// POST 请求
const response = await request.post('/api/users', { name: 'John' })

// PUT 请求
const response = await request.put('/api/users/1', { name: 'Jane' })

// DELETE 请求
const response = await request.delete('/api/users/1')
```

### 2. 聊天机器人 API

```typescript
import ChatbotAPI from '@/api/chatbot'

// 获取会话列表
const sessions = await ChatbotAPI.getSessions()

// 创建新会话
const newSession = await ChatbotAPI.createSession({
  title: '新的对话',
  model: 'gpt-4'
})

// 发送消息
const response = await ChatbotAPI.sendMessage({
  sessionId: 'session-id',
  content: '你好',
  model: 'gpt-4'
})

// 流式消息（实时响应）
const eventSource = ChatbotAPI.sendMessageStream({
  sessionId: 'session-id',
  content: '请解释一下Vue3的组合式API',
  model: 'deepseek-r1'
})

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data)
  console.log('收到消息:', data)
}

eventSource.onerror = (error) => {
  console.error('连接错误:', error)
  eventSource.close()
}
```

## 后端 API 接口规范

### 响应格式

所有 API 响应都应遵循以下格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "success": true
}
```

### 聊天相关接口

#### 1. 获取会话列表
- **URL**: `GET /api/chat/sessions`
- **参数**: 
  - `page`: 页码（可选）
  - `pageSize`: 每页数量（可选）
  - `keyword`: 搜索关键词（可选）
- **响应**:
```json
{
  "code": 200,
  "data": {
    "sessions": [
      {
        "id": "session-1",
        "title": "对话标题",
        "lastMessage": "最后一条消息",
        "timestamp": "2024-01-20 10:30:00",
        "isPinned": false,
        "messages": []
      }
    ],
    "total": 10
  }
}
```

#### 2. 创建新会话
- **URL**: `POST /api/chat/sessions`
- **参数**:
```json
{
  "title": "新对话",
  "model": "gpt-4"
}
```

#### 3. 发送消息
- **URL**: `POST /api/chat/messages`
- **参数**:
```json
{
  "sessionId": "session-1",
  "content": "用户消息内容",
  "model": "gpt-4"
}
```
- **响应**:
```json
{
  "code": 200,
  "data": {
    "message": {
      "id": "msg-1",
      "type": "assistant",
      "content": "AI回复内容",
      "timestamp": "2024-01-20 10:30:00",
      "thinking": "思考过程（可选，用于deepseek-r1等模型）"
    },
    "sessionId": "session-1"
  }
}
```

#### 4. 流式消息
- **URL**: `GET /api/chat/messages/stream`
- **参数**: 通过 URL 参数传递
  - `sessionId`: 会话ID
  - `content`: 消息内容
  - `model`: 模型名称
  - `token`: 认证令牌（可选）
- **响应**: Server-Sent Events 格式

```
data: {"type": "thinking", "content": "正在思考..."}

data: {"type": "message", "content": "这是回复的第一部分"}

data: {"type": "message", "content": "这是回复的第二部分"}

data: {"type": "done", "messageId": "msg-123"}
```

## 错误处理

### HTTP 状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码
- `200`: 成功
- `401`: 登录过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

## 环境配置

### 开发环境 (.env.development)
```
VITE_API_BASE_URL=http://localhost:8080/api
```

### 生产环境 (.env.production)
```
VITE_API_BASE_URL=https://your-api-domain.com/api
```

## 注意事项

1. 所有请求都会自动添加认证 token（如果存在）
2. 请求和响应都会在控制台打印日志，便于调试
3. 错误会自动显示用户友好的提示信息
4. 支持文件上传和下载功能
5. 流式消息使用 Server-Sent Events，适合实时聊天场景
