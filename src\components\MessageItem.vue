<template>
  <div class="message-item" :class="messageClass">
    <div class="message-content">
      <!-- 用户头像 -->
      <div v-if="message.type === 'user'" class="avatar user-avatar">
        <n-icon size="20">
          <PersonIcon />
        </n-icon>
      </div>

      <!-- AI头像 -->
      <div v-else class="avatar ai-avatar">
        <n-icon size="20">
          <RobotIcon />
        </n-icon>
      </div>

      <!-- 消息气泡 -->
      <div class="message-bubble">
        <!-- AI思考过程 (仅对deepseek-r1等模型显示) -->
        <div v-if="message.thinking && message.type === 'assistant'" class="thinking-process">
          <div class="thinking-header">
            <n-icon size="14" class="thinking-icon">
              <BulbIcon />
            </n-icon>
            <span class="thinking-label">思考过程</span>
          </div>
          <div class="thinking-content">{{ message.thinking }}</div>
        </div>

        <!-- 消息内容 -->
        <div class="message-text">
          <div class="text-content">{{ message.content }}</div>
          <div class="message-time">{{ formatTime(message.timestamp) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { NIcon } from 'naive-ui'
import {
  Person as PersonIcon,
  Bulb as BulbIcon
} from '@vicons/ionicons5'

// 定义组件选项
defineOptions({
  name: 'MessageItem'
})

// 自定义机器人图标
const RobotIcon = () => h('svg', {
  viewBox: '0 0 24 24',
  fill: 'currentColor'
}, [
  h('path', {
    d: 'M12 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 7h-2v1c0 2.76-2.24 5-5 5s-5-2.24-5-5V9H4c-1.1 0-2 .9-2 2v3c0 1.1.9 2 2 2h1v3c0 1.1.9 2 2 2h2c1.1 0 2-.9 2-2v-3h2v3c0 1.1.9 2 2 2h2c1.1 0 2-.9 2-2v-3h1c1.1 0 2-.9 2-2v-3c0-1.1-.9-2-2-2z'
  })
])

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  thinking?: string
}

interface Props {
  message: Message
}

const props = defineProps<Props>()

const messageClass = computed(() => ({
  'user-message': props.message.type === 'user',
  'ai-message': props.message.type === 'assistant'
}))

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.message-item {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-avatar {
  background: #1890ff;
  color: white;
}

.ai-avatar {
  background: #52c41a;
  color: white;
}

.message-bubble {
  max-width: 70%;
  min-width: 100px;
}

.thinking-process {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  font-size: 13px;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  color: #666;
  font-weight: 500;
}

.thinking-icon {
  color: #faad14;
}

.thinking-label {
  font-size: 12px;
}

.thinking-content {
  color: #666;
  line-height: 1.5;
  font-style: italic;
}

.message-text {
  background: #f0f0f0;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
}

.user-message .message-text {
  background: #1890ff;
  color: white;
}

.ai-message .message-text {
  background: #f0f0f0;
  color: #333;
}

.text-content {
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 11px;
  margin-top: 6px;
  opacity: 0.7;
}

.user-message .message-time {
  text-align: right;
  color: rgba(255, 255, 255, 0.8);
}

.ai-message .message-time {
  color: #999;
}

/* 消息气泡箭头效果 */
.user-message .message-text::before {
  content: '';
  position: absolute;
  top: 12px;
  right: -6px;
  width: 0;
  height: 0;
  border-left: 6px solid #1890ff;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

.ai-message .message-text::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -6px;
  width: 0;
  height: 0;
  border-right: 6px solid #f0f0f0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}
</style>
