<template>
  <div class="chat-sidebar">
    <!-- 新建聊天按钮 -->
    <div class="new-chat-section">
      <n-button
        type="primary"
        block
        @click="createNewChat"
        class="new-chat-btn"
      >
        <n-icon size="16" style="margin-right: 8px;">
          <AddIcon />
        </n-icon>
        新建对话
      </n-button>
    </div>

    <!-- 聊天列表 -->
    <div class="chat-list">
      <div class="chat-list-header">
        <h3>历史对话</h3>
      </div>

      <div class="chat-items">
        <div
          v-for="chat in sortedChatList"
          :key="chat.id"
          class="chat-item"
          :class="{ active: chat.id === activeChatId }"
          @click="selectChat(chat.id)"
          @mouseenter="hoveredChatId = chat.id"
          @mouseleave="hoveredChatId = null"
        >
          <!-- 置顶图标 -->
          <div v-if="chat.isPinned" class="pin-icon">
            <n-icon size="12">
              <PinIcon />
            </n-icon>
          </div>

          <!-- 聊天内容 -->
          <div class="chat-content">
            <div class="chat-title">{{ chat.title }}</div>
            <div class="chat-preview">{{ chat.lastMessage }}</div>
            <div class="chat-time">{{ formatTime(chat.timestamp) }}</div>
          </div>

          <!-- 操作菜单 -->
          <div
            v-if="hoveredChatId === chat.id"
            class="chat-actions"
          >
            <n-dropdown
              :options="getMenuOptions(chat)"
              @select="handleMenuSelect"
              trigger="click"
            >
              <n-button
                text
                size="small"
                class="menu-trigger"
              >
                <n-icon size="16">
                  <MoreIcon />
                </n-icon>
              </n-button>
            </n-dropdown>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import {
  NButton,
  NIcon,
  NDropdown,
  useMessage
} from 'naive-ui'
import {
  Add as AddIcon,
  EllipsisVertical as MoreIcon,
  Pin as PinIcon
} from '@vicons/ionicons5'

interface ChatItem {
  id: string
  title: string
  lastMessage: string
  timestamp: string
  isPinned: boolean
}

interface Props {
  chatList: ChatItem[]
  activeChatId: string
}

interface Emits {
  (e: 'select-chat', chatId: string): void
  (e: 'pin-chat', chatId: string): void
  (e: 'rename-chat', chatId: string, newTitle: string): void
  (e: 'delete-chat', chatId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const hoveredChatId = ref<string | null>(null)
const currentMenuChatId = ref<string>('')

// 排序聊天列表：置顶的在前面
const sortedChatList = computed(() => {
  return [...props.chatList].sort((a, b) => {
    if (a.isPinned && !b.isPinned) return -1
    if (!a.isPinned && b.isPinned) return 1
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  })
})

const selectChat = (chatId: string) => {
  emit('select-chat', chatId)
}

const createNewChat = () => {
  // 这里可以触发创建新聊天的逻辑
  message.success('创建新对话功能待实现')
}

const getMenuOptions = (chat: ChatItem) => {
  currentMenuChatId.value = chat.id
  return [
    {
      label: chat.isPinned ? '取消置顶' : '置顶',
      key: 'pin',
      icon: () => h(NIcon, { size: 14 }, { default: () => h(PinIcon) })
    },
    {
      label: '重命名',
      key: 'rename',
      icon: () => h(NIcon, { size: 14 }, { default: () => h('svg', {
        viewBox: '0 0 24 24',
        fill: 'currentColor'
      }, [
        h('path', { d: 'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z' })
      ]) })
    },
    {
      label: '删除',
      key: 'delete',
      icon: () => h(NIcon, { size: 14 }, { default: () => h('svg', {
        viewBox: '0 0 24 24',
        fill: 'currentColor'
      }, [
        h('path', { d: 'M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z' })
      ]) })
    }
  ]
}

const handleMenuSelect = (key: string) => {
  const chatId = currentMenuChatId.value

  switch (key) {
    case 'pin':
      emit('pin-chat', chatId)
      break
    case 'rename':
      // 简单的重命名实现
      const newTitle = prompt('请输入新的对话标题：')
      if (newTitle && newTitle.trim()) {
        emit('rename-chat', chatId, newTitle.trim())
      }
      break
    case 'delete':
      if (confirm('确定要删除这个对话吗？')) {
        emit('delete-chat', chatId)
      }
      break
  }
}

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}
</script>

<style scoped>
.chat-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.new-chat-section {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.new-chat-btn {
  height: 40px;
}

.chat-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chat-list-header {
  padding: 16px 16px 8px;
  border-bottom: 1px solid #e0e0e0;
}

.chat-list-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.chat-items {
  flex: 1;
  overflow-y: auto;
}

.chat-item {
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  display: flex;
  align-items: flex-start;
}

.chat-item:hover {
  background-color: #f5f5f5;
}

.chat-item.active {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.pin-icon {
  margin-right: 8px;
  margin-top: 2px;
  color: #ff9800;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-preview {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-time {
  font-size: 11px;
  color: #ccc;
}

.chat-actions {
  margin-left: 8px;
  opacity: 0.8;
}

.menu-trigger {
  padding: 4px;
}
</style>
