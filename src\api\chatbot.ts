import request from '../utils/request'

// 聊天相关接口类型定义
export interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  thinking?: string // deepseek-r1思考过程
}

export interface ChatSession {
  id: string
  title: string
  lastMessage: string
  timestamp: string
  isPinned: boolean
  messages: ChatMessage[]
}

export interface SendMessageRequest {
  sessionId: string
  content: string
  model?: string // AI模型类型，如 'gpt-4', 'deepseek-r1' 等
}

export interface SendMessageResponse {
  message: ChatMessage
  sessionId: string
}

export interface CreateSessionRequest {
  title?: string
  model?: string
}

export interface CreateSessionResponse {
  session: ChatSession
}

export interface UpdateSessionRequest {
  sessionId: string
  title?: string
  isPinned?: boolean
}

export interface GetSessionsResponse {
  sessions: ChatSession[]
  total: number
}

export interface GetSessionDetailResponse {
  session: ChatSession
}

// 聊天API类
class ChatbotAPI {
  // 获取聊天会话列表
  static async getSessions(params?: {
    page?: number
    pageSize?: number
    keyword?: string
  }): Promise<GetSessionsResponse> {
    const response = await request.get<GetSessionsResponse>('/chat/sessions', { params })
    return response.data
  }

  // 获取单个会话详情
  static async getSessionDetail(sessionId: string): Promise<GetSessionDetailResponse> {
    const response = await request.get<GetSessionDetailResponse>(`/chat/sessions/${sessionId}`)
    return response.data
  }

  // 创建新的聊天会话
  static async createSession(data: CreateSessionRequest): Promise<CreateSessionResponse> {
    const response = await request.post<CreateSessionResponse>('/chat/sessions', data)
    return response.data
  }

  // 更新会话信息（重命名、置顶等）
  static async updateSession(data: UpdateSessionRequest): Promise<void> {
    const { sessionId, ...updateData } = data
    await request.put(`/chat/sessions/${sessionId}`, updateData)
  }

  // 删除会话
  static async deleteSession(sessionId: string): Promise<void> {
    await request.delete(`/chat/sessions/${sessionId}`)
  }

  // 发送消息
  static async sendMessage(data: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await request.post<SendMessageResponse>('/chat/messages', data)
    return response.data
  }

  // 获取会话的消息历史
  static async getMessages(sessionId: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<{ messages: ChatMessage[], total: number }> {
    const response = await request.get<{ messages: ChatMessage[], total: number }>(`/chat/sessions/${sessionId}/messages`, { params })
    return response.data
  }

  // 删除消息
  static async deleteMessage(messageId: string): Promise<void> {
    await request.delete(`/chat/messages/${messageId}`)
  }

  // 重新生成回复
  static async regenerateMessage(messageId: string): Promise<SendMessageResponse> {
    const response = await request.post<SendMessageResponse>(`/chat/messages/${messageId}/regenerate`)
    return response.data
  }

  // 获取可用的AI模型列表
  static async getAvailableModels(): Promise<{ models: Array<{ id: string, name: string, description: string }> }> {
    const response = await request.get<{ models: Array<{ id: string, name: string, description: string }> }>('/chat/models')
    return response.data
  }

  // 流式发送消息（Server-Sent Events）
  static sendMessageStream(data: SendMessageRequest): EventSource {
    const token = localStorage.getItem('token')
    const params = new URLSearchParams({
      sessionId: data.sessionId,
      content: data.content,
      model: data.model || 'gpt-4',
      ...(token && { token }) // 如果有token，通过URL参数传递
    })

    const baseURL = import.meta.env.VITE_API_BASE_URL || '/api'
    const url = `${baseURL}/chat/messages/stream?${params.toString()}`

    const eventSource = new EventSource(url, {
      withCredentials: true
    })

    return eventSource
  }
}

export default ChatbotAPI