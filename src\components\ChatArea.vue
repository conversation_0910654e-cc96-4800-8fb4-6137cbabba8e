<template>
  <div class="chat-area">
    <!-- 消息列表 -->
    <div class="messages-container" ref="messagesContainer">
      <div class="messages-list">
        <MessageItem
          v-for="message in messages"
          :key="message.id"
          :message="message"
        />
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <n-input
          v-model:value="inputMessage"
          type="textarea"
          placeholder="输入您的问题..."
          :autosize="{ minRows: 1, maxRows: 4 }"
          @keydown="handleKeydown"
          class="message-input"
        />
        <n-button
          type="primary"
          :disabled="!inputMessage.trim()"
          @click="sendMessage"
          class="send-button"
        >
          <n-icon size="16">
            <SendIcon />
          </n-icon>
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { NInput, NButton, NIcon } from 'naive-ui'
import { Send as SendIcon } from '@vicons/ionicons5'
import MessageItem from './MessageItem.vue'

// 定义组件选项
defineOptions({
  name: 'ChatArea'
})

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  thinking?: string
}

interface Props {
  messages: Message[]
}

interface Emits {
  (e: 'send-message', content: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const inputMessage = ref('')
const messagesContainer = ref<HTMLElement>()

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    nextTick(() => {
      scrollToBottom()
    })
  },
  { deep: true }
)

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const sendMessage = () => {
  const content = inputMessage.value.trim()
  if (content) {
    emit('send-message', content)
    inputMessage.value = ''
  }
}

const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    sendMessage()
  }
}
</script>

<style scoped>
.chat-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

.messages-list {
  max-width: 800px;
  margin: 0 auto;
}

.input-area {
  border-top: 1px solid #e0e0e0;
  background: white;
  padding: 16px 20px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
}

.send-button {
  height: 40px;
  width: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
