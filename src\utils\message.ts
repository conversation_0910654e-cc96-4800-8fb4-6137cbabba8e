// 简单的消息提示工具
// 在组件中使用时，可以直接导入 useMessage from 'naive-ui'

export interface MessageOptions {
  type: 'success' | 'error' | 'warning' | 'info'
  content: string
  duration?: number
}

// 简单的控制台日志版本（用于非组件环境）
export const simpleMessage = {
  success: (content: string) => {
    console.log(`✅ ${content}`)
  },
  error: (content: string) => {
    console.error(`❌ ${content}`)
  },
  warning: (content: string) => {
    console.warn(`⚠️ ${content}`)
  },
  info: (content: string) => {
    console.info(`ℹ️ ${content}`)
  }
}

// 在组件中使用的示例：
/*
<script setup>
import { useMessage } from 'naive-ui'

const message = useMessage()

const showSuccess = () => {
  message.success('操作成功！')
}
</script>
*/
