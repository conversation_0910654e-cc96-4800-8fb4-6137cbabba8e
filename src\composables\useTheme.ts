import { ref, watch, readonly } from 'vue'

// 全局主题状态
const isDark = ref(false)

// 初始化主题
const initTheme = () => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDark.value = savedTheme === 'dark'
  } else {
    // 检测系统主题偏好
    isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }
}

// 切换主题
const toggleTheme = () => {
  isDark.value = !isDark.value
}

// 监听主题变化并保存到localStorage
watch(isDark, (newValue) => {
  localStorage.setItem('theme', newValue ? 'dark' : 'light')
}, { immediate: false })

// 监听系统主题变化
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
mediaQuery.addEventListener('change', (e) => {
  // 只有在没有手动设置主题时才跟随系统
  if (!localStorage.getItem('theme')) {
    isDark.value = e.matches
  }
})

// 初始化
initTheme()

// 导出主题管理函数
export const useTheme = () => {
  return {
    isDark: readonly(isDark),
    toggleTheme
  }
}

// 为了兼容性，也导出单独的函数
export { isDark, toggleTheme }
